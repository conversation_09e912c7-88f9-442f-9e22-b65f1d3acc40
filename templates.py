"""
Template configurations for different storyboard styles and use cases
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, <PERSON><PERSON>, List
import base64
from io import BytesIO
from PIL import Image, ImageDraw

@dataclass
class TemplateConfig:
    name: str
    primary_color: Tuple[int, int, int]
    secondary_color: Tuple[int, int, int]
    background_color: Tuple[int, int, int]
    font_family: str
    layout_style: str = "corporate"
    accent_color: Tuple[int, int, int] = (52, 152, 219)
    text_color: Tuple[int, int, int] = (44, 62, 80)
    description: str = ""
    industry: str = "General"
    icon: str = "🎨"
    gradient_colors: List[Tuple[int, int, int]] = None
    border_style: str = "solid"
    shadow_enabled: bool = True

    def __post_init__(self):
        if self.gradient_colors is None:
            self.gradient_colors = [self.primary_color, self.secondary_color]

class PresentationTemplate(Enum):
    # Business & Professional Templates
    PROFESSIONAL = TemplateConfig(
        name="Professional Business",
        primary_color=(44, 62, 80),
        secondary_color=(52, 152, 219),
        background_color=(248, 249, 250),
        font_family="Arial",
        layout_style="corporate",
        accent_color=(52, 152, 219),
        text_color=(44, 62, 80),
        description="Clean, professional design perfect for business presentations",
        industry="Business",
        icon="💼"
    )

    EXECUTIVE = TemplateConfig(
        name="Executive Suite",
        primary_color=(25, 25, 25),
        secondary_color=(220, 165, 0),
        background_color=(250, 250, 250),
        font_family="Times New Roman",
        layout_style="executive",
        accent_color=(220, 165, 0),
        text_color=(25, 25, 25),
        description="Sophisticated design for C-level presentations",
        industry="Business",
        icon="👔",
        shadow_enabled=True
    )

    # Technology Templates
    TECH_MODERN = TemplateConfig(
        name="Tech Modern",
        primary_color=(13, 71, 161),
        secondary_color=(0, 188, 212),
        background_color=(245, 245, 245),
        font_family="Roboto",
        layout_style="modern",
        accent_color=(76, 175, 80),
        text_color=(33, 33, 33),
        description="Modern tech-focused design with clean lines",
        industry="Technology",
        icon="💻",
        gradient_colors=[(13, 71, 161), (0, 188, 212)]
    )

    STARTUP = TemplateConfig(
        name="Startup Vibes",
        primary_color=(103, 58, 183),
        secondary_color=(255, 87, 34),
        background_color=(250, 250, 250),
        font_family="Montserrat",
        layout_style="dynamic",
        accent_color=(255, 193, 7),
        text_color=(66, 66, 66),
        description="Energetic design perfect for startup presentations",
        industry="Technology",
        icon="🚀",
        gradient_colors=[(103, 58, 183), (255, 87, 34)]
    )

    # Creative & Design Templates
    CREATIVE = TemplateConfig(
        name="Creative & Colorful",
        primary_color=(155, 89, 182),
        secondary_color=(241, 196, 15),
        background_color=(253, 254, 254),
        font_family="Calibri",
        layout_style="modern",
        accent_color=(241, 196, 15),
        text_color=(44, 62, 80),
        description="Vibrant and engaging design for creative projects",
        industry="Creative",
        icon="🎨"
    )

    MINIMAL = TemplateConfig(
        name="Minimal & Clean",
        primary_color=(45, 52, 54),
        secondary_color=(99, 110, 114),
        background_color=(255, 255, 255),
        font_family="Helvetica",
        layout_style="clean",
        accent_color=(99, 110, 114),
        text_color=(45, 52, 54),
        description="Ultra-clean minimalist design with focus on content",
        industry="General",
        icon="⚪"
    )

    # Healthcare Templates
    HEALTHCARE = TemplateConfig(
        name="Healthcare Professional",
        primary_color=(0, 121, 107),
        secondary_color=(76, 175, 80),
        background_color=(248, 255, 248),
        font_family="Open Sans",
        layout_style="healthcare",
        accent_color=(139, 195, 74),
        text_color=(33, 37, 41),
        description="Clean, trustworthy design for healthcare presentations",
        industry="Healthcare",
        icon="🏥",
        gradient_colors=[(0, 121, 107), (76, 175, 80)]
    )

    MEDICAL = TemplateConfig(
        name="Medical Research",
        primary_color=(21, 101, 192),
        secondary_color=(3, 169, 244),
        background_color=(250, 251, 252),
        font_family="Source Sans Pro",
        layout_style="clinical",
        accent_color=(0, 150, 136),
        text_color=(38, 50, 56),
        description="Professional medical and research presentation style",
        industry="Healthcare",
        icon="🔬"
    )

    # Retail & E-commerce Templates
    RETAIL = TemplateConfig(
        name="Retail Experience",
        primary_color=(233, 30, 99),
        secondary_color=(255, 152, 0),
        background_color=(255, 248, 225),
        font_family="Poppins",
        layout_style="retail",
        accent_color=(255, 193, 7),
        text_color=(66, 66, 66),
        description="Vibrant design perfect for retail and e-commerce",
        industry="Retail",
        icon="🛍️",
        gradient_colors=[(233, 30, 99), (255, 152, 0)]
    )

    ECOMMERCE = TemplateConfig(
        name="E-commerce Modern",
        primary_color=(67, 56, 202),
        secondary_color=(16, 185, 129),
        background_color=(249, 250, 251),
        font_family="Inter",
        layout_style="ecommerce",
        accent_color=(245, 101, 101),
        text_color=(31, 41, 55),
        description="Modern e-commerce focused design with conversion emphasis",
        industry="Retail",
        icon="💳"
    )

    # Financial Services Templates
    FINANCE = TemplateConfig(
        name="Financial Services",
        primary_color=(13, 71, 161),
        secondary_color=(0, 96, 100),
        background_color=(250, 250, 250),
        font_family="Georgia",
        layout_style="financial",
        accent_color=(0, 150, 136),
        text_color=(37, 47, 63),
        description="Professional and trustworthy design for financial services",
        industry="Finance",
        icon="💰"
    )

    # Education Templates
    EDUCATION = TemplateConfig(
        name="Educational",
        primary_color=(121, 85, 72),
        secondary_color=(255, 152, 0),
        background_color=(255, 253, 245),
        font_family="Lato",
        layout_style="educational",
        accent_color=(255, 193, 7),
        text_color=(62, 39, 35),
        description="Warm and engaging design for educational content",
        industry="Education",
        icon="📚"
    )

def get_template_config(template: PresentationTemplate) -> TemplateConfig:
    """Get configuration for a specific template"""
    return template.value

def get_available_templates() -> Dict[str, str]:
    """Get list of available templates with descriptions"""
    return {template.name: template.value.description for template in PresentationTemplate}

def get_templates_by_industry() -> Dict[str, List[PresentationTemplate]]:
    """Group templates by industry"""
    industry_groups = {}
    for template in PresentationTemplate:
        industry = template.value.industry
        if industry not in industry_groups:
            industry_groups[industry] = []
        industry_groups[industry].append(template)
    return industry_groups

def generate_template_preview(template: PresentationTemplate, width: int = 200, height: int = 120) -> str:
    """Generate a base64 encoded preview image for a template"""
    config = template.value

    # Create preview image
    img = Image.new('RGB', (width, height), config.background_color)
    draw = ImageDraw.Draw(img)

    # Draw header bar with primary color
    header_height = height // 4
    draw.rectangle([0, 0, width, header_height], fill=config.primary_color)

    # Draw accent elements
    accent_width = width // 8
    draw.rectangle([accent_width, header_height, accent_width * 2, height], fill=config.accent_color)

    # Draw secondary color elements
    draw.rectangle([width - accent_width * 2, header_height + 10, width - 10, height - 10], fill=config.secondary_color)

    # Add some text-like rectangles
    text_y = header_height + 15
    draw.rectangle([accent_width * 3, text_y, width - 20, text_y + 8], fill=config.text_color)
    draw.rectangle([accent_width * 3, text_y + 15, width - 40, text_y + 23], fill=(*config.text_color[:3], 128))

    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    img_str = base64.b64encode(buffer.getvalue()).decode()

    return f"data:image/png;base64,{img_str}"

# Industry-specific prompt enhancements
INDUSTRY_PROMPTS = {
    "E-commerce": {
        "context_enhancement": "Focus on online shopping experience, product discovery, cart management, and delivery tracking",
        "key_stages": ["Discovery", "Product Research", "Purchase Decision", "Checkout", "Delivery", "Post-Purchase"],
        "common_emotions": ["curious", "excited", "hesitant", "confident", "satisfied", "delighted"]
    },
    
    "SaaS/Software": {
        "context_enhancement": "Emphasize user onboarding, feature adoption, problem-solving, and value realization",
        "key_stages": ["Problem Recognition", "Solution Research", "Trial/Demo", "Onboarding", "Feature Adoption", "Renewal"],
        "common_emotions": ["frustrated", "hopeful", "confused", "accomplished", "productive", "loyal"]
    },
    
    "Healthcare": {
        "context_enhancement": "Focus on patient care, appointment scheduling, treatment journey, and follow-up care",
        "key_stages": ["Symptom Recognition", "Provider Search", "Appointment", "Consultation", "Treatment", "Recovery"],
        "common_emotions": ["concerned", "anxious", "hopeful", "relieved", "recovering", "grateful"]
    },
    
    "Financial Services": {
        "context_enhancement": "Emphasize trust, security, financial goals, and long-term relationships",
        "key_stages": ["Financial Need", "Research", "Consultation", "Decision", "Implementation", "Monitoring"],
        "common_emotions": ["worried", "cautious", "informed", "confident", "secure", "empowered"]
    },
    
    "Education": {
        "context_enhancement": "Focus on learning journey, skill development, progress tracking, and achievement",
        "key_stages": ["Learning Need", "Course Discovery", "Enrollment", "Learning", "Assessment", "Certification"],
        "common_emotions": ["curious", "motivated", "challenged", "accomplished", "proud", "inspired"]
    }
}

def get_industry_enhancement(industry: str) -> Dict[str, any]:
    """Get industry-specific enhancements for story generation"""
    return INDUSTRY_PROMPTS.get(industry, {
        "context_enhancement": "Focus on user needs, experience quality, and value delivery",
        "key_stages": ["Awareness", "Consideration", "Decision", "Onboarding", "Usage", "Advocacy"],
        "common_emotions": ["interested", "evaluating", "deciding", "learning", "satisfied", "loyal"]
    })

"""
Brand customization and advanced styling options
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum
import base64
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
import colorsys

@dataclass
class BrandConfig:
    """Brand configuration for customized storyboards"""
    brand_name: str = ""
    logo_data: Optional[str] = None  # Base64 encoded logo
    logo_position: str = "top-right"  # top-left, top-right, bottom-left, bottom-right
    logo_size: Tuple[int, int] = (100, 50)
    
    # Color palette
    primary_color: Tuple[int, int, int] = (52, 152, 219)
    secondary_color: Tuple[int, int, int] = (46, 204, 113)
    accent_color: Tuple[int, int, int] = (241, 196, 15)
    background_color: Tuple[int, int, int] = (248, 249, 250)
    text_color: Tuple[int, int, int] = (44, 62, 80)
    
    # Typography
    primary_font: str = "Arial"
    secondary_font: str = "Arial"
    font_sizes: Dict[str, int] = None
    
    # Layout preferences
    spacing: int = 20
    border_radius: int = 8
    shadow_enabled: bool = True
    gradient_enabled: bool = False
    
    def __post_init__(self):
        if self.font_sizes is None:
            self.font_sizes = {
                "title": 24,
                "subtitle": 18,
                "body": 14,
                "caption": 12
            }

class ColorPalette:
    """Utility class for color palette generation and manipulation"""
    
    @staticmethod
    def generate_complementary_colors(base_color: Tuple[int, int, int]) -> List[Tuple[int, int, int]]:
        """Generate complementary colors from a base color"""
        r, g, b = [x/255.0 for x in base_color]
        h, s, v = colorsys.rgb_to_hsv(r, g, b)
        
        colors = []
        # Original color
        colors.append(base_color)
        
        # Complementary (180 degrees)
        comp_h = (h + 0.5) % 1.0
        comp_r, comp_g, comp_b = colorsys.hsv_to_rgb(comp_h, s, v)
        colors.append((int(comp_r * 255), int(comp_g * 255), int(comp_b * 255)))
        
        # Triadic colors (120 degrees apart)
        tri1_h = (h + 0.33) % 1.0
        tri1_r, tri1_g, tri1_b = colorsys.hsv_to_rgb(tri1_h, s, v)
        colors.append((int(tri1_r * 255), int(tri1_g * 255), int(tri1_b * 255)))
        
        tri2_h = (h + 0.67) % 1.0
        tri2_r, tri2_g, tri2_b = colorsys.hsv_to_rgb(tri2_h, s, v)
        colors.append((int(tri2_r * 255), int(tri2_g * 255), int(tri2_b * 255)))
        
        return colors
    
    @staticmethod
    def generate_monochromatic_palette(base_color: Tuple[int, int, int], count: int = 5) -> List[Tuple[int, int, int]]:
        """Generate monochromatic color palette"""
        r, g, b = [x/255.0 for x in base_color]
        h, s, v = colorsys.rgb_to_hsv(r, g, b)
        
        colors = []
        for i in range(count):
            # Vary the value (brightness) while keeping hue and saturation
            new_v = max(0.2, min(1.0, v + (i - count//2) * 0.15))
            new_r, new_g, new_b = colorsys.hsv_to_rgb(h, s, new_v)
            colors.append((int(new_r * 255), int(new_g * 255), int(new_b * 255)))
        
        return colors
    
    @staticmethod
    def get_predefined_palettes() -> Dict[str, List[Tuple[int, int, int]]]:
        """Get predefined color palettes"""
        return {
            "Ocean": [(52, 152, 219), (46, 204, 113), (26, 188, 156), (22, 160, 133), (39, 174, 96)],
            "Sunset": [(231, 76, 60), (230, 126, 34), (241, 196, 15), (243, 156, 18), (211, 84, 0)],
            "Forest": [(39, 174, 96), (46, 204, 113), (26, 188, 156), (22, 160, 133), (16, 172, 132)],
            "Purple": [(155, 89, 182), (142, 68, 173), (155, 89, 182), (187, 143, 206), (174, 129, 194)],
            "Corporate": [(52, 73, 94), (44, 62, 80), (52, 152, 219), (41, 128, 185), (93, 109, 126)],
            "Warm": [(231, 76, 60), (192, 57, 43), (230, 126, 34), (211, 84, 0), (243, 156, 18)],
            "Cool": [(52, 152, 219), (41, 128, 185), (46, 204, 113), (39, 174, 96), (26, 188, 156)],
            "Monochrome": [(44, 62, 80), (52, 73, 94), (99, 110, 114), (149, 165, 166), (189, 195, 199)]
        }

class AdvancedImageStyle(Enum):
    """Advanced image styles with detailed descriptions"""
    
    # Original styles
    SKETCHY_BW_GRAPHIC = "Sketchy B&W Graphic"
    CARTOON = "Cartoon-like"
    REALISTIC = "Realistic pictures"
    
    # New artistic styles
    WATERCOLOR = "Watercolor painting style with soft, flowing colors and organic textures"
    OIL_PAINTING = "Oil painting style with rich textures, visible brushstrokes, and classical composition"
    DIGITAL_ART = "Modern digital art with clean lines, vibrant colors, and contemporary aesthetics"
    PENCIL_SKETCH = "Detailed pencil sketch with fine lines, shading, and artistic composition"
    INK_DRAWING = "Bold ink drawing with strong contrasts, clean lines, and minimal color"
    PASTEL = "Soft pastel artwork with gentle colors, smooth transitions, and dreamy atmosphere"
    VECTOR_ART = "Clean vector illustration with geometric shapes, flat colors, and modern design"
    MINIMALIST = "Minimalist style with simple shapes, limited colors, and clean composition"
    VINTAGE = "Vintage illustration style with retro colors, classic typography, and nostalgic feel"
    ISOMETRIC = "Isometric 3D style with geometric precision and modern technical aesthetic"
    
    # Style combinations
    CARTOON_PROFESSIONAL = "Professional cartoon style - clean, business-appropriate with cartoon elements"
    REALISTIC_ARTISTIC = "Artistic realism - photorealistic with artistic enhancements and mood"
    SKETCH_COLORFUL = "Colorful sketch - hand-drawn sketch style with vibrant color accents"
    MINIMAL_ELEGANT = "Elegant minimalism - sophisticated simplicity with premium feel"

@dataclass
class CustomStyleTemplate:
    """Custom style template that users can save and reuse"""
    name: str
    description: str
    base_style: AdvancedImageStyle
    color_palette: List[Tuple[int, int, int]]
    style_modifiers: List[str]  # Additional style descriptors
    mood_keywords: List[str]    # Mood and atmosphere keywords
    created_by: str = "User"
    
    def to_prompt_enhancement(self) -> str:
        """Convert style template to prompt enhancement text"""
        base_desc = self.base_style.value
        modifiers = ", ".join(self.style_modifiers)
        mood = ", ".join(self.mood_keywords)
        
        return f"{base_desc}. Style modifiers: {modifiers}. Mood: {mood}."

def get_style_combinations() -> Dict[str, List[AdvancedImageStyle]]:
    """Get predefined style combinations"""
    return {
        "Business Professional": [AdvancedImageStyle.REALISTIC, AdvancedImageStyle.MINIMAL_ELEGANT],
        "Creative & Artistic": [AdvancedImageStyle.WATERCOLOR, AdvancedImageStyle.DIGITAL_ART],
        "Technical & Modern": [AdvancedImageStyle.VECTOR_ART, AdvancedImageStyle.ISOMETRIC],
        "Warm & Approachable": [AdvancedImageStyle.CARTOON_PROFESSIONAL, AdvancedImageStyle.PASTEL],
        "Bold & Dynamic": [AdvancedImageStyle.INK_DRAWING, AdvancedImageStyle.DIGITAL_ART],
        "Classic & Timeless": [AdvancedImageStyle.OIL_PAINTING, AdvancedImageStyle.VINTAGE]
    }

def create_brand_preview(brand_config: BrandConfig, width: int = 300, height: int = 200) -> str:
    """Create a preview of the brand configuration"""
    img = Image.new('RGB', (width, height), brand_config.background_color)
    draw = ImageDraw.Draw(img)
    
    # Draw brand colors as swatches
    swatch_size = 30
    colors = [brand_config.primary_color, brand_config.secondary_color, brand_config.accent_color]
    
    for i, color in enumerate(colors):
        x = 20 + i * (swatch_size + 10)
        y = 20
        draw.rectangle([x, y, x + swatch_size, y + swatch_size], fill=color)
    
    # Draw sample text
    try:
        font = ImageFont.truetype(f"{brand_config.primary_font}.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((20, 70), brand_config.brand_name or "Brand Name", fill=brand_config.text_color, font=font)
    draw.text((20, 100), "Sample storyboard content", fill=brand_config.text_color)
    
    # Add logo placeholder if logo exists
    if brand_config.logo_data:
        draw.rectangle([width-80, 20, width-20, 50], outline=brand_config.primary_color, width=2)
        draw.text((width-75, 30), "LOGO", fill=brand_config.primary_color)
    
    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return f"data:image/png;base64,{img_str}"

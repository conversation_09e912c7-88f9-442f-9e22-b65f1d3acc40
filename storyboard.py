import os
from openai import Async<PERSON><PERSON>A<PERSON>
from typing import List, Union, Literal
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
import requests
from io import BytesIO
from pptx import Presentation
from pptx.util import Cm, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from pptx.dml.color import RGBColor
from pptx.oxml.xmlchemy import OxmlElement
from pptx.enum.dml import MSO_FILL
from enum import Enum
import asyncio
from PIL import Image, ImageDraw, ImageFont
from utils.error_handler import handle_errors, StoryboardError

# Error handler is imported and used in the functions below

# Import brand customization for advanced styles
try:
    from brand_customization import AdvancedImageStyle, CustomStyleTemplate, BrandConfig
    from templates import TemplateConfig
except ImportError:
    # Fallback if brand_customization is not available
    AdvancedImageStyle = None
    CustomStyleTemplate = None
    BrandConfig = None
    TemplateConfig = None


class ImageModel(Enum):
    OPENAI_DALLE_3 = "OpenAI DALL-e 3"


class AspectRatio(Enum):
    WIDESCREEN = "16:9"
    SQUARE = "1:1"
    VERTICAL = "9:16"


class Style(Enum):
    SKETCHY_BW_GRAPHIC = "Sketchy B&W Graphic"
    CARTOON = "Cartoon-like"
    REALISTIC = "Realistic pictures"
    CUSTOM = "Custom"


class Shot(Enum):
    FULL_SHOT = "Full shot"
    AMERICAN_SHOT = "American shot"
    MEDIUM_SHOT = "Medium shot"
    MEDIUM_CLOSEUP_SHOT = "Medium close-up shot"
    CLOSEUP_SHOT = "Close-up shot"


class Language(Enum):
    ENGLISH = "en"
    SPANISH = "es"


def enhance_image_prompt_with_advanced_style(base_prompt: str, style: Style, style_description: str = "",
                                           advanced_style: 'AdvancedImageStyle' = None,
                                           custom_template: 'CustomStyleTemplate' = None) -> str:
    """
    Enhance the base image prompt with advanced styling options
    """
    enhanced_prompt = base_prompt

    # Apply advanced style if available
    if AdvancedImageStyle and advanced_style:
        enhanced_prompt += f" Style: {advanced_style.value}."

    # Apply custom style template if available
    if CustomStyleTemplate and custom_template:
        template_enhancement = custom_template.to_prompt_enhancement()
        enhanced_prompt += f" {template_enhancement}"

    # Apply basic style description
    if style_description:
        enhanced_prompt += f" Additional style notes: {style_description}."

    # Add style-specific enhancements based on the basic style
    if style == Style.SKETCHY_BW_GRAPHIC:
        enhanced_prompt += " Hand-drawn sketch style, black and white with minimal color accents, artistic line work."
    elif style == Style.CARTOON:
        enhanced_prompt += " Cartoon illustration style, vibrant colors, clean lines, friendly and approachable."
    elif style == Style.REALISTIC:
        enhanced_prompt += " Photorealistic style, high detail, natural lighting, professional photography quality."
    elif style == Style.CUSTOM and style_description:
        enhanced_prompt += f" Custom style: {style_description}."

    return enhanced_prompt
    FRENCH = "fr"
    GERMAN = "de"
    CHINESE = "zh"


API_KEY = os.environ.get("OPENAI_KEY")

POOL_SIZE = 10


def SubElement(parent, tagname, **kwargs):
    element = OxmlElement(tagname)
    element.attrib.update(kwargs)
    parent.append(element)

    return element


def change_border_color(cell, border_color):
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()

    borders = ["a:lnL", "a:lnR", "a:lnT", "a:lnB"]

    if cell.fill.type == MSO_FILL.SOLID:
        fill_color = cell.fill.fore_color.rgb
    else:
        fill_color = RGBColor(0, 0, 0)

    cell.fill.background()

    for border in borders:
        ln = SubElement(tcPr, border)
        solidFill = SubElement(ln, 'a:solidFill')
        SubElement(solidFill, 'a:srgbClr', val=border_color)

    cell.fill.solid()
    cell.fill.fore_color.rgb = fill_color


class JourneyStep(BaseModel):
    step_name: str = Field(description="Concise step name like 'Select payment method' or 'Browse products'")
    step_description: str = Field(description="Detailed description of what the customer does, thinks, and feels during this step")
    scene_description: str = Field(description="Vivid, specific description of the visual scene that represents this step - include environment, objects, actions, and emotions")
    photo_shooting: Literal[tuple(s.name for s in Shot)] = Field(description="Best camera shot type for this scene")
    customer_emotion: str = Field(description="Primary emotion the customer feels during this step (e.g., excited, frustrated, confident, confused)")
    pain_points: List[str] = Field(description="Specific challenges or friction points the customer might experience", default=[])
    opportunities: List[str] = Field(description="Opportunities to improve the customer experience at this step", default=[])

def create_journey_step_model(include_emotion: bool = True, include_challenges: bool = True):
    """Create a dynamic JourneyStep model based on inclusion parameters"""
    fields = {
        'step_name': (str, Field(description="Concise step name like 'Select payment method' or 'Browse products'")),
        'scene_description': (str, Field(description="Vivid, specific description of the visual scene that represents this step - include environment, objects, and actions")),
        'photo_shooting': (Literal[tuple(s.name for s in Shot)], Field(description="Best camera shot type for this scene")),
        'opportunities': (List[str], Field(description="Opportunities to improve the customer experience at this step", default=[]))
    }

    # Conditional description field
    if include_emotion:
        fields['step_description'] = (str, Field(description="Detailed description of what the customer does, thinks, and feels during this step"))
        fields['scene_description'] = (str, Field(description="Vivid, specific description of the visual scene that represents this step - include environment, objects, actions, and emotions"))
    else:
        fields['step_description'] = (str, Field(description="Detailed description of what the customer does and thinks during this step"))

    # Conditional emotion field
    if include_emotion:
        fields['customer_emotion'] = (str, Field(description="Primary emotion the customer feels during this step (e.g., excited, frustrated, confident, confused)"))

    # Conditional challenges field
    if include_challenges:
        fields['pain_points'] = (List[str], Field(description="Specific challenges or friction points the customer might experience", default=[]))

    return type('DynamicJourneyStep', (BaseModel,), {'__annotations__': {k: v[0] for k, v in fields.items()}, **{k: v[1] for k, v in fields.items()}})


class JourneyStage(BaseModel):
    stage_name: str = Field(description="Clear stage name like 'Discovery', 'Evaluation', 'Purchase', 'Onboarding', 'Usage'")
    stage_description: str = Field(description="Brief description of what happens during this stage")
    steps: List[JourneyStep] = Field(description="Detailed steps within this customer journey stage")
    stage_goal: str = Field(description="Primary goal the customer wants to achieve in this stage")

def create_journey_stage_model(include_emotion: bool = True, include_challenges: bool = True):
    """Create a dynamic JourneyStage model based on inclusion parameters"""
    DynamicJourneyStep = create_journey_step_model(include_emotion, include_challenges)

    return type('DynamicJourneyStage', (BaseModel,), {
        '__annotations__': {
            'stage_name': str,
            'stage_description': str,
            'steps': List[DynamicJourneyStep],
            'stage_goal': str
        },
        'stage_name': Field(description="Clear stage name like 'Discovery', 'Evaluation', 'Purchase', 'Onboarding', 'Usage'"),
        'stage_description': Field(description="Brief description of what happens during this stage"),
        'steps': Field(description="Detailed steps within this customer journey stage"),
        'stage_goal': Field(description="Primary goal the customer wants to achieve in this stage")
    })


class PersonaProfile(BaseModel):
    name: str = Field(description="Persona's first name")
    age_range: str = Field(description="Age range like '25-35' or 'early 30s'")
    occupation: str = Field(description="Job title or profession")
    tech_savviness: str = Field(description="Technology comfort level (beginner, intermediate, advanced)")
    primary_motivations: List[str] = Field(description="Main drivers and motivations")
    frustrations: List[str] = Field(description="Common pain points and frustrations")
    preferred_channels: List[str] = Field(description="Preferred communication and interaction channels")
    personality_traits: List[str] = Field(description="Key personality characteristics")


class JourneyStory(BaseModel):
    story_name: str = Field(description="Engaging story title like 'Sarah's Smart Home Journey' or 'Mike's Fitness Transformation'")
    persona_profile: PersonaProfile = Field(description="Detailed persona information")
    persona_physical_description: str = Field(description="Detailed physical appearance for consistent image generation - include hair color/style, clothing style, accessories, build, facial features")
    story_context: str = Field(description="Background context that sets up why this journey is happening")
    stages: List[JourneyStage] = Field(description="Complete customer journey stages from awareness to advocacy")
    key_insights: List[str] = Field(description="Important insights and learnings from this journey", default=[])

def create_journey_story_model(include_emotion: bool = True, include_challenges: bool = True):
    """Create a dynamic JourneyStory model based on inclusion parameters"""
    DynamicJourneyStage = create_journey_stage_model(include_emotion, include_challenges)

    return type('DynamicJourneyStory', (BaseModel,), {
        '__annotations__': {
            'story_name': str,
            'persona_profile': PersonaProfile,
            'persona_physical_description': str,
            'story_context': str,
            'stages': List[DynamicJourneyStage],
            'key_insights': List[str]
        },
        'story_name': Field(description="Engaging story title like 'Sarah's Smart Home Journey' or 'Mike's Fitness Transformation'"),
        'persona_profile': Field(description="Detailed persona information"),
        'persona_physical_description': Field(description="Detailed physical appearance for consistent image generation - include hair color/style, clothing style, accessories, build, facial features"),
        'story_context': Field(description="Background context that sets up why this journey is happening"),
        'stages': Field(description="Complete customer journey stages from awareness to advocacy"),
        'key_insights': Field(description="Important insights and learnings from this journey", default=[])
    })


def calculate_cell_position(table, row, col):
    table_gf = table._graphic_frame
    x = table_gf._element.xfrm.off.x
    y = table_gf._element.xfrm.off.y

    cell_left = x
    cell_top = y
    cell_height = table.rows[0].height
    cell_width = table.columns[0].width

    for r in range(row):
        cell_top += table.rows[r].height

    for c in range(col):
        cell_left += table.columns[c].width

    cell_height = table.rows[row].height
    cell_width = table.columns[col].width

    return cell_left, cell_top, cell_width, cell_height


def enhance_image_prompt(base_prompt: str, style_description: str, persona_description: str, shot_type: str, step_context: str) -> str:
    """Enhanced prompt engineering for better image quality and consistency"""

    # Composition and framing instructions
    composition_guide = {
        "FULL_SHOT": "full body shot showing the entire person and environment, wide angle view",
        "AMERICAN_SHOT": "three-quarter shot from knees up, showing person and immediate surroundings",
        "MEDIUM_SHOT": "waist-up shot focusing on upper body and facial expressions",
        "MEDIUM_CLOSEUP_SHOT": "chest-up shot emphasizing facial expressions and hand gestures",
        "CLOSEUP_SHOT": "head and shoulders shot with focus on facial details and emotions"
    }

    # Enhanced prompt structure
    enhanced_prompt = f"""
    COMPOSITION: {composition_guide.get(shot_type, shot_type)}

    CHARACTER: {persona_description} - maintain consistent appearance, clothing, and physical features

    SCENE: {base_prompt}

    CONTEXT: {step_context}

    STYLE: {style_description}

    QUALITY MODIFIERS: high quality, professional composition, good lighting, clear details, consistent character design, engaging scene

    TECHNICAL: sharp focus, well-lit, professional photography style, clean background when appropriate
    """.strip()

    return enhanced_prompt


def convert_preview_to_story(preview_data: dict, include_emotion: bool = True, include_challenges: bool = True):
    """Convert edited preview data back to story format for storyboard generation"""

    # Create a simple object that mimics the story structure
    class SimpleStory:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class SimpleStage:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class SimpleStep:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    # Convert persona data
    persona_data = preview_data['persona']
    persona_profile = PersonaProfile(
        name=persona_data['name'],
        age_range=persona_data['age_range'],
        occupation=persona_data['occupation'],
        tech_savviness=persona_data.get('tech_savviness', 'intermediate'),
        primary_motivations=persona_data.get('primary_motivations', ['Efficiency', 'Quality']),
        frustrations=persona_data.get('frustrations', ['Slow service', 'Poor quality']),
        preferred_channels=persona_data.get('preferred_channels', ['Mobile app', 'Website']),
        personality_traits=persona_data.get('traits', ['Professional', 'Goal-oriented'])
    )

    # Convert stages and steps
    converted_stages = []
    for stage_data in preview_data['stages']:
        converted_steps = []

        for step_data in stage_data['steps']:
            # Map shot type to enum name
            shot_mapping = {
                "Full shot": "FULL_SHOT",
                "American shot": "AMERICAN_SHOT",
                "Medium shot": "MEDIUM_SHOT",
                "Medium close-up shot": "MEDIUM_CLOSEUP_SHOT",
                "Close-up shot": "CLOSEUP_SHOT"
            }

            # Create step object
            step_attrs = {
                'step_name': step_data['name'],
                'step_description': step_data['description'],
                'scene_description': step_data['description'],  # Use description as scene description
                'photo_shooting': shot_mapping.get(step_data['shot_type'], 'MEDIUM_SHOT'),
                'opportunities': []  # Default empty list
            }

            # Add emotion if included
            if include_emotion and step_data.get('emotion') and step_data['emotion'] != 'N/A':
                step_attrs['customer_emotion'] = step_data['emotion']

            # Add challenges if included (default empty for edited data)
            if include_challenges:
                step_attrs['pain_points'] = []

            converted_step = SimpleStep(**step_attrs)
            converted_steps.append(converted_step)

        converted_stage = SimpleStage(
            stage_name=stage_data['name'],
            stage_description=stage_data['description'],
            steps=converted_steps,
            stage_goal=stage_data['goal']
        )
        converted_stages.append(converted_stage)

    # Create the complete story object
    story = SimpleStory(
        story_name=preview_data['story_name'],
        persona_profile=persona_profile,
        persona_physical_description="Professional appearance with modern clothing style",  # Default description
        story_context=preview_data['story_context'],
        stages=converted_stages,
        key_insights=[]  # Default empty list
    )

    return story

def create_consistent_persona_description(persona_desc: str, physical_desc: str) -> str:
    """Create more detailed and consistent persona description"""
    return f"""
    {physical_desc}

    CONSISTENCY REQUIREMENTS:
    - Same facial features, hair color, and style throughout all images
    - Consistent clothing style and color palette
    - Same body type and posture characteristics
    - Maintain the same age appearance
    - Keep consistent accessories (glasses, jewelry, etc.)

    PERSONALITY TRAITS: {persona_desc}
    """.strip()


@handle_errors(fallback_value=None)
async def generate_image(client: object, image_model: ImageModel, aspect_ratio: AspectRatio, prompt: str,
                        style: Style = Style.SKETCHY_BW_GRAPHIC, style_description: str = "",
                        advanced_style: 'AdvancedImageStyle' = None, custom_template: 'CustomStyleTemplate' = None,
                        max_retries: int = 3) -> BytesIO:
    """Enhanced image generation with retry logic, better error handling, and advanced styling"""

    # Enhance the prompt with advanced styling
    enhanced_prompt = enhance_image_prompt_with_advanced_style(
        prompt, style, style_description, advanced_style, custom_template
    )

    if image_model == ImageModel.OPENAI_DALLE_3:
        if aspect_ratio == AspectRatio.SQUARE:
            image_size = "1024x1024"
        elif aspect_ratio == AspectRatio.WIDESCREEN:
            image_size = "1792x1024"
        elif aspect_ratio == AspectRatio.VERTICAL:
            image_size = "1024x1792"

        for attempt in range(max_retries):
            try:
                response = await client.images.generate(
                    model="dall-e-3",
                    size=image_size,
                    prompt=enhanced_prompt,
                    quality="hd",  # Use HD quality for better results
                    style="natural"  # More realistic and professional style
                )

                http_response = requests.get(response.data[0].url, timeout=30)
                http_response.raise_for_status()

                buffer = BytesIO()
                buffer.write(http_response.content)
                buffer.seek(0)

                return buffer

            except Exception as e:
                if attempt == max_retries - 1:
                    raise Exception(f"Failed to generate image after {max_retries} attempts: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    raise Exception(f"Unsupported image model: {image_model}")


async def generate_image_with_fallback(client: object, image_model: ImageModel, aspect_ratio: AspectRatio, prompt: str,
                                     step_index: int, style: Style = Style.SKETCHY_BW_GRAPHIC, style_description: str = "",
                                     advanced_style: 'AdvancedImageStyle' = None, custom_template: 'CustomStyleTemplate' = None) -> BytesIO:
    """Generate image with fallback handling and advanced styling"""
    try:
        return await generate_image(client, image_model, aspect_ratio, prompt, style, style_description, advanced_style, custom_template)
    except Exception as e:
        print(f"Failed to generate image for step {step_index}: {str(e)}")
        return create_placeholder_image(aspect_ratio)


@handle_errors(fallback_value=BytesIO())
def create_placeholder_image(aspect_ratio: AspectRatio) -> BytesIO:
    """Create a simple placeholder image when generation fails"""
    from PIL import Image, ImageDraw, ImageFont

    if aspect_ratio == AspectRatio.SQUARE:
        size = (1024, 1024)
    elif aspect_ratio == AspectRatio.WIDESCREEN:
        size = (1792, 1024)
    elif aspect_ratio == AspectRatio.VERTICAL:
        size = (1024, 1792)

    # Create simple placeholder
    img = Image.new('RGB', size, color=(240, 240, 240))
    draw = ImageDraw.Draw(img)

    # Add placeholder text
    text = "Image\nGeneration\nFailed"
    try:
        font = ImageFont.truetype("Arial.ttf", 60)
    except:
        font = ImageFont.load_default()

    # Center the text
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2

    draw.text((x, y), text, fill=(100, 100, 100), font=font, align='center')

    # Save to buffer
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)

    return buffer


@handle_errors(fallback_value={'error': 'Story preview generation failed'})
def generate_story_preview(description: str, expectations_list: List[str], nb_steps: int, avoided_terms: List[str], include_emotion: bool = True, include_challenges: bool = True, output_language: str = "English") -> dict:
    """Generate a preview of the story structure without images"""

    instructions = f"""
    Create a comprehensive customer journey story structure for preview.
    Focus on creating a clear narrative arc with realistic stages and steps.
    This is a preview generation, so prioritize story structure over detailed descriptions.
    Determine the optimal number of steps needed for a complete and meaningful customer journey.

    LANGUAGE: Generate all content in {output_language}.
    """

    if nb_steps:
        instructions += f"\n\nSTEP LIMIT: Create exactly {nb_steps} steps total across all stages."

    if avoided_terms:
        instructions += f"\n\nTERMS TO AVOID: Do not use these terms: {', '.join(avoided_terms)}"

    # Conditional content inclusion
    if not include_emotion:
        instructions += "\n\nEMOTION HANDLING: Do not include customer emotions in the step descriptions. Focus only on actions and outcomes."

    if not include_challenges:
        instructions += "\n\nCHALLENGE HANDLING: Do not include pain points or challenges in the journey steps. Focus on positive experiences only."

    if expectations_list:
        expectations = "\n\nINSIGHTS TO INTEGRATE:\n" + \
                       "\n".join([f"• {e.strip()}" for e in expectations_list if e.strip()])
    else:
        expectations = ""

    prompt_sections = [
        f"PROJECT CONTEXT:\n{description}",
        instructions,
        expectations,
        "\nDELIVERABLE: Generate a story structure for preview purposes."
    ]

    prompt = "\n".join([section for section in prompt_sections if section.strip()])

    model = ChatOpenAI(api_key=API_KEY, model="gpt-4o", timeout=60)
    DynamicJourneyStory = create_journey_story_model(include_emotion, include_challenges)
    struct_model = model.with_structured_output(DynamicJourneyStory)

    try:
        story = struct_model.invoke(prompt)

        # Convert to preview format
        preview_data = {
            'story_name': story.story_name,
            'persona': {
                'name': story.persona_profile.name,
                'age_range': story.persona_profile.age_range,
                'occupation': story.persona_profile.occupation,
                'traits': story.persona_profile.personality_traits[:3]
            },
            'story_context': story.story_context,
            'stages': []
        }

        for stage in story.stages:
            stage_data = {
                'name': stage.stage_name,
                'description': stage.stage_description,
                'goal': stage.stage_goal,
                'steps': []
            }

            for step in stage.steps:
                step_data = {
                    'name': step.step_name,
                    'description': step.step_description[:200] + "..." if len(step.step_description) > 200 else step.step_description,
                    'shot_type': Shot[step.photo_shooting].value
                }

                # Add emotion only if included
                if include_emotion and hasattr(step, 'customer_emotion'):
                    step_data['emotion'] = step.customer_emotion
                else:
                    step_data['emotion'] = 'N/A'

                stage_data['steps'].append(step_data)

            preview_data['stages'].append(stage_data)

        return preview_data

    except Exception as e:
        return {'error': f"Preview generation failed: {str(e)}"}


async def generate_storyboard(set_progress, description: str, expectations_list: List[str], image_model: ImageModel, nb_steps: Union[None, int], aspect_ratio: AspectRatio, style: Style, style_description_input: str, avoided_terms: List[str], include_emotion: bool = True, include_challenges: bool = True, output_language: str = "English", edited_story_data: dict = None, brand_config=None, template_config=None) -> Presentation:
    if aspect_ratio == AspectRatio.SQUARE:
        image_ratio = 1
    elif aspect_ratio == AspectRatio.WIDESCREEN:
        image_ratio = 1.75
    elif aspect_ratio == AspectRatio.VERTICAL:
        image_ratio = 0.57

    if style == Style.SKETCHY_BW_GRAPHIC:
        style_description = "Sketchy graphic style, quick, rough and imprecise marker strokes, not too detailed, with few strokes, white background, black color for lines and a light gray scale for shades."
    elif style == Style.CARTOON:
        style_description = "Cartoon-like images."
    elif style == Style.REALISTIC:
        style_description = "Photo-realistic pictures."
    elif style == Style.CUSTOM:
        style_description = style_description_input

    # Enhanced story generation instructions
    instructions = f"""
    Create a comprehensive customer journey story that follows a realistic progression from initial need awareness to post-purchase advocacy.

    LANGUAGE: Generate all content in {output_language}.

    STORY REQUIREMENTS:
    - Build a complete narrative arc with clear beginning, middle, and end
    {"- Include realistic emotional ups and downs throughout the journey" if include_emotion else "- Focus on actions and outcomes without emphasizing emotions"}
    {"- Show how the customer's needs, concerns, and emotions evolve" if include_emotion else "- Show how the customer's needs and requirements evolve"}
    - Include both digital and physical touchpoints where relevant
    - Make each step feel authentic and relatable
    - Determine the optimal number of steps needed for a complete and meaningful journey (typically 5-12 steps)

    JOURNEY STRUCTURE:
    - Start with need recognition or problem awareness
    - Include research and evaluation phases
    - Show decision-making process {"with potential hesitations" if include_challenges else "focusing on positive progression"}
    - Cover purchase/onboarding experience
    - Include usage, support, and potential advocacy stages

    PERSONA DEVELOPMENT:
    - Create a realistic, well-rounded character with specific demographics
    - Include relevant background that influences their journey
    - Show personality traits that affect their decision-making
    {"- Include realistic motivations and pain points" if include_challenges else "- Include realistic motivations and positive drivers"}

    SCENE DESCRIPTIONS:
    - Make visual descriptions specific and detailed
    - Include environmental context (location, time of day, setting)
    {"- Show emotions through body language and facial expressions" if include_emotion else "- Focus on actions and interactions rather than emotional expressions"}
    - Include relevant props, devices, or objects in scenes
    """

    if nb_steps:
        instructions += f"\n\nSTEP LIMIT: Create exactly {nb_steps} steps total across all stages."

    if avoided_terms:
        instructions += f"\n\nTERMS TO AVOID: Do not use these terms in any descriptions: {', '.join(avoided_terms)}"

    # Additional conditional instructions
    if not include_emotion:
        instructions += "\n\nEMOTION HANDLING: Minimize emotional language. Focus on functional aspects, actions, and outcomes rather than feelings."

    if not include_challenges:
        instructions += "\n\nCHALLENGE HANDLING: Avoid including pain points, frustrations, or negative experiences. Present a smooth, positive customer journey."

    # Enhanced expectations integration
    if expectations_list:
        expectations = "\n\nINSIGHTS TO INTEGRATE:\nWeave these insights naturally into the customer journey:\n" + \
                       "\n".join([f"• {e.strip()}" for e in expectations_list if e.strip()])
    else:
        expectations = ""

    # Construct comprehensive prompt
    prompt_sections = [
        f"PROJECT CONTEXT:\n{description}",
        instructions,
        expectations,
        "\nDELIVERABLE: Generate a complete customer journey story with detailed persona, stages, and steps that can be visualized in a professional storyboard."
    ]

    prompt = "\n".join([section for section in prompt_sections if section.strip()])

    model = ChatOpenAI(api_key=API_KEY,
                       model="o1",
                       timeout=180)  # Increased timeout for more complex generation

    DynamicJourneyStory = create_journey_story_model(include_emotion, include_challenges)
    struct_model = model.with_structured_output(DynamicJourneyStory)

    # Use edited story data if available, otherwise generate new story
    if edited_story_data:
        set_progress("Using edited story data...")
        # Convert edited preview data to story format
        story = convert_preview_to_story(edited_story_data, include_emotion, include_challenges)
    else:
        set_progress("Story generation...")
        story = struct_model.invoke(prompt)

    prs = Presentation()
    prs.slide_width = Cm(36)
    prs.slide_height = Cm(18)

    blank_slide_layout = prs.slide_layouts[6]
    slide = prs.slides.add_slide(blank_slide_layout)
    shapes = slide.shapes

    # Apply brand/template background color
    slide.background.fill.solid()
    if brand_config and brand_config.background_color:
        bg_color = brand_config.background_color
        slide.background.fill.fore_color.rgb = RGBColor(bg_color[0], bg_color[1], bg_color[2])
    elif template_config and template_config.background_color:
        bg_color = template_config.background_color
        slide.background.fill.fore_color.rgb = RGBColor(bg_color[0], bg_color[1], bg_color[2])
    else:
        slide.background.fill.fore_color.rgb = RGBColor(0xF8, 0xF9, 0xFA)

    cols = sum([len(stage.steps) for stage in story.stages])
    rows = 4

    # Enhanced story title with brand/template typography
    title_box = shapes.add_textbox(Cm(1.5), Cm(1.5), Cm(16), Cm(3))
    title_box.text = story.story_name
    title_box.text_frame.word_wrap = True

    # Apply brand/template font and colors
    if brand_config and brand_config.primary_font:
        title_box.text_frame.paragraphs[0].font.name = brand_config.primary_font
    elif template_config and template_config.font_family:
        title_box.text_frame.paragraphs[0].font.name = template_config.font_family
    else:
        title_box.text_frame.paragraphs[0].font.name = "Arial"

    title_box.text_frame.paragraphs[0].font.bold = True
    title_box.text_frame.paragraphs[0].font.size = Pt(26)

    # Apply brand/template text color
    if brand_config and brand_config.text_color:
        text_color = brand_config.text_color
        title_box.text_frame.paragraphs[0].font.color.rgb = RGBColor(text_color[0], text_color[1], text_color[2])
    elif template_config and template_config.text_color:
        text_color = template_config.text_color
        title_box.text_frame.paragraphs[0].font.color.rgb = RGBColor(text_color[0], text_color[1], text_color[2])
    else:
        title_box.text_frame.paragraphs[0].font.color.rgb = RGBColor(0x2C, 0x3E, 0x50)

    title_box.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE

    # Add brand logo if available
    if brand_config and brand_config.logo_data:
        try:
            import base64
            from io import BytesIO

            # Decode base64 logo
            logo_data = base64.b64decode(brand_config.logo_data)
            logo_buffer = BytesIO(logo_data)

            # Position logo based on brand_config.logo_position
            logo_width, logo_height = brand_config.logo_size
            logo_width_cm = Cm(logo_width / 28.35)  # Convert pixels to cm
            logo_height_cm = Cm(logo_height / 28.35)

            if brand_config.logo_position == "top-right":
                logo_left = Cm(36) - logo_width_cm - Cm(1)
                logo_top = Cm(1)
            elif brand_config.logo_position == "top-left":
                logo_left = Cm(1)
                logo_top = Cm(1)
            elif brand_config.logo_position == "bottom-right":
                logo_left = Cm(36) - logo_width_cm - Cm(1)
                logo_top = Cm(18) - logo_height_cm - Cm(1)
            else:  # bottom-left
                logo_left = Cm(1)
                logo_top = Cm(18) - logo_height_cm - Cm(1)

            shapes.add_picture(logo_buffer, logo_left, logo_top, logo_width_cm, logo_height_cm)
        except Exception as e:
            print(f"Failed to add logo: {str(e)}")

    # Enhanced persona section with better layout
    img_left = Cm(20)
    img_top = Cm(1.5)
    img_size = Cm(3.5)

    img_width = img_size * image_ratio
    img_height = img_size
    img_left -= (img_width - img_size)

    # Persona background with subtle shadow
    persona_bg = shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE,
                                  Cm(19.5) - (img_width - img_size), Cm(1.2),
                                  Cm(15) + (img_width - img_size), Cm(3.8))
    persona_bg.fill.solid()
    persona_bg.fill.fore_color.rgb = RGBColor(255, 255, 255)
    persona_bg.line.color.rgb = RGBColor(0xE1, 0xE8, 0xED)
    persona_bg.line.width = Pt(1)
    persona_bg.adjustments[0] = 0.05

    # Enhanced persona description
    persona_text = f"""
    {story.persona_profile.name} | {story.persona_profile.age_range} | {story.persona_profile.occupation}

    {story.story_context}

    Key traits: {', '.join(story.persona_profile.personality_traits[:3])}
    """.strip()

    persona_textbox = shapes.add_textbox(Cm(23.5), Cm(1.5), Cm(10.5), Cm(3.5))
    persona_textbox.text = persona_text
    persona_textbox.text_frame.word_wrap = True
    persona_textbox.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
    persona_textbox.text_frame.paragraphs[0].font.size = Pt(10)
    persona_textbox.text_frame.paragraphs[0].font.name = "Arial"
    persona_textbox.text_frame.paragraphs[0].font.color.rgb = RGBColor(0x34, 0x49, 0x5E)

    set_progress("Persona generation...")
    client = AsyncOpenAI(api_key=API_KEY)

    # Enhanced persona image prompt
    consistent_persona = create_consistent_persona_description(
        f"{story.persona_profile.name} is {', '.join(story.persona_profile.personality_traits[:2])}",
        story.persona_physical_description
    )

    persona_prompt = enhance_image_prompt(
        "Professional headshot portrait in a clean, modern setting",
        style_description,
        consistent_persona,
        "MEDIUM_CLOSEUP_SHOT",
        "Confident and approachable expression, looking directly at camera"
    )

    buffer = await generate_image(client, image_model, aspect_ratio, persona_prompt)

    shapes.add_picture(buffer, img_left, img_top, img_width, img_height)

    # Enhanced table layout with better proportions
    img_cell_height = Cm(6)
    img_height = min(int(Cm(34) / cols / image_ratio), img_cell_height)
    img_width = image_ratio * img_height
    table_height = Cm(0.8) + Cm(1.2) + img_height + Cm(4)

    # Modern table background with brand/template colors
    table_bg = shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, Cm(1), Cm(6), Cm(34), table_height + Cm(1))
    table_bg.fill.solid()

    # Apply brand/template primary color for table background
    if brand_config and brand_config.primary_color:
        primary_color = brand_config.primary_color
        table_bg.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
    elif template_config and template_config.primary_color:
        primary_color = template_config.primary_color
        table_bg.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
    else:
        table_bg.fill.fore_color.rgb = RGBColor(0x2C, 0x3E, 0x50)  # Professional dark blue

    table_bg.adjustments[0] = 0.04

    table = shapes.add_table(rows, cols, Cm(1.5), Cm(6), Cm(33), table_height).table

    table.rows[0].height = Cm(0.8)  # Stage header
    table.rows[1].height = Cm(1.2)  # Step names
    table.rows[2].height = img_height  # Images
    table.rows[3].height = Cm(4)  # Descriptions

    idx_step = 0
    for stage in story.stages:
        # Enhanced stage header
        cell = table.cell(0, idx_step)

        if len(stage.steps) > 1:
            cell.merge(table.cell(0, idx_step + len(stage.steps) - 1))

        cell.text = stage.stage_name.upper()
        cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(11)
        cell.text_frame.paragraphs[0].font.name = "Arial"
        cell.text_frame.paragraphs[0].font.fill.solid()
        cell.text_frame.paragraphs[0].font.fill.fore_color.rgb = RGBColor(255, 255, 255)
        cell.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
        cell.fill.solid()

        # Apply brand/template primary color for stage headers
        if brand_config and brand_config.primary_color:
            primary_color = brand_config.primary_color
            cell.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
        elif template_config and template_config.primary_color:
            primary_color = template_config.primary_color
            cell.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
        else:
            cell.fill.fore_color.rgb = RGBColor(0x2C, 0x3E, 0x50)

        for j, step in enumerate(stage.steps):
            # Enhanced step name formatting
            step_cell = table.cell(1, idx_step + j)
            step_cell.text = step.step_name
            step_cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            step_cell.text_frame.paragraphs[0].font.bold = True
            step_cell.text_frame.paragraphs[0].font.size = Pt(10)
            step_cell.text_frame.paragraphs[0].font.name = "Arial"
            step_cell.text_frame.paragraphs[0].font.fill.solid()
            step_cell.text_frame.paragraphs[0].font.fill.fore_color.rgb = RGBColor(255, 255, 255)
            step_cell.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
            step_cell.text_frame.word_wrap = True
            step_cell.fill.solid()

            # Apply brand/template secondary color for step headers
            if brand_config and brand_config.secondary_color:
                secondary_color = brand_config.secondary_color
                step_cell.fill.fore_color.rgb = RGBColor(secondary_color[0], secondary_color[1], secondary_color[2])
            elif template_config and template_config.secondary_color:
                secondary_color = template_config.secondary_color
                step_cell.fill.fore_color.rgb = RGBColor(secondary_color[0], secondary_color[1], secondary_color[2])
            else:
                step_cell.fill.fore_color.rgb = RGBColor(0x34, 0x49, 0x5E)

            # Image cell with brand/template background
            img_cell = table.cell(2, idx_step + j)
            img_cell.fill.solid()

            # Apply brand/template primary color for image cell background
            if brand_config and brand_config.primary_color:
                primary_color = brand_config.primary_color
                img_cell.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
            elif template_config and template_config.primary_color:
                primary_color = template_config.primary_color
                img_cell.fill.fore_color.rgb = RGBColor(primary_color[0], primary_color[1], primary_color[2])
            else:
                img_cell.fill.fore_color.rgb = RGBColor(0x2C, 0x3E, 0x50)

            # Enhanced description cell
            desc_cell = table.cell(3, idx_step + j)

            # Create rich description with emotion and context
            rich_description = f"""
            {step.step_description}

            {f"Emotion: {step.customer_emotion}" if include_emotion and hasattr(step, 'customer_emotion') else ""}
            """.strip()

            if include_challenges and hasattr(step, 'pain_points') and step.pain_points:
                rich_description += f"\n\nChallenges: {', '.join(step.pain_points[:2])}"

            desc_cell.text = rich_description
            desc_cell.text_frame.paragraphs[0].font.size = Pt(9)
            desc_cell.text_frame.paragraphs[0].font.name = "Arial"
            desc_cell.text_frame.paragraphs[0].font.color.rgb = RGBColor(0x2C, 0x3E, 0x50)
            desc_cell.text_frame.word_wrap = True
            desc_cell.text_frame.vertical_anchor = MSO_ANCHOR.TOP
            desc_cell.fill.solid()
            desc_cell.fill.fore_color.rgb = RGBColor(0xF8, 0xF9, 0xFA)

        idx_step += len(stage.steps)

    # Enhanced border styling
    for row in table.rows:
        for cell in row.cells:
            change_border_color(cell, "2C3E50")

    # Enhanced scenes generation with better prompts
    img_prompts = []
    consistent_persona = create_consistent_persona_description(
        f"{story.persona_profile.name} - {', '.join(story.persona_profile.personality_traits[:2])}",
        story.persona_physical_description
    )

    for stage in story.stages:
        for step in stage.steps:
            # Create context-rich prompt for each scene
            step_context = f"""
            Customer journey stage: {stage.stage_name}
            Customer goal: {stage.stage_goal}
            {f"Customer emotion: {step.customer_emotion}" if include_emotion and hasattr(step, 'customer_emotion') else ""}
            Step context: {step.step_description}
            """.strip()

            enhanced_prompt = enhance_image_prompt(
                step.scene_description,
                style_description,
                consistent_persona,
                step.photo_shooting,
                step_context
            )

            img_prompts.append(enhanced_prompt)

    set_progress(f"Scenes generation... 0/{cols}")
    tasks = []
    buffers = []
    failed_images = []

    # Create tasks with better error handling
    for col in range(cols):
        task = asyncio.create_task(
            generate_image_with_fallback(client, image_model, aspect_ratio, img_prompts[col], col, style, style_description_input)
        )
        tasks.append(task)

    # Process in batches with progress updates
    for i in range(0, len(tasks), POOL_SIZE):
        pool = tasks[i:i + POOL_SIZE]

        try:
            results = await asyncio.gather(*pool, return_exceptions=True)

            for j, result in enumerate(results):
                if isinstance(result, Exception):
                    # Create fallback image or placeholder
                    failed_images.append(i + j)
                    buffers.append(create_placeholder_image(aspect_ratio))
                else:
                    buffers.append(result)

        except Exception as e:
            # Handle batch failure
            for _ in pool:
                buffers.append(create_placeholder_image(aspect_ratio))

        set_progress(f"Scenes generation... {min(i + len(pool), cols)}/{cols}")

    if failed_images:
        print(f"Warning: {len(failed_images)} images failed to generate and were replaced with placeholders")

    # Enhanced image placement with better positioning
    for col, buffer in enumerate(buffers):
        cell_left, cell_top, cell_width, cell_height = calculate_cell_position(table, 2, col)

        # Adjust for better visual alignment
        cell_top = Cm(8.2)  # Fine-tuned positioning

        # Calculate optimal image size maintaining aspect ratio
        img_height = min(cell_width / image_ratio, cell_height * 0.9)  # Leave some padding
        img_width = image_ratio * img_height

        # Center the image in the cell
        cell_left += (cell_width - img_width) / 2
        cell_top += (cell_height - img_height) / 2

        # Account for table borders with better spacing
        cell_left += Cm(0.03)
        img_width -= Cm(0.06)
        img_height = img_width / image_ratio

        try:
            shapes.add_picture(buffer, cell_left, cell_top, img_width, img_height)
        except Exception as e:
            print(f"Failed to add image {col}: {str(e)}")
            # Continue without this image rather than failing completely

    return prs
